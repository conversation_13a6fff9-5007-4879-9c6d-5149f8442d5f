# FitTracker Pro 🏋️‍♂️

Une application de fitness moderne et intuitive développée avec Flutter.

## ✨ Fonctionnalités

### 🏠 Page d'accueil moderne
- **Salutation personnalisée** avec emoji selon l'heure
- **Notifications** avec indicateur visuel
- **Design moderne** avec couleurs et ombres élégantes

### 📅 Calendrier horizontal interactif
- Navigation fluide entre les jours
- Sélection de date intuitive
- Indicateur visuel pour aujourd'hui
- Design responsive avec animations

### 🔥 Section Streak & Motivation
- Suivi des jours consécutifs d'entraînement
- Messages motivationnels
- Design avec gradient et ombres

### 📊 Statistiques du jour
- **Calories brûlées** avec progression
- **Nombre de pas** avec objectif
- **Temps actif** quotidien
- **Consommation d'eau** avec suivi

### 💪 Entraînement du jour
- Suggestion d'entraînement personnalisée
- Informations détaillées (durée, niveau)
- Bouton de démarrage rapide

### 🎯 Défis de la semaine
- Objectifs hebdomadaires
- Barres de progression visuelles
- Suivi des calories et séances

### 📈 Activités récentes
- Historique des derniers entraînements
- Détails de chaque activité
- Horodatage et métriques

## 🎨 Design inspiré des meilleures apps
- **Strava** : Calendrier horizontal et suivi d'activités
- **MyFitnessPal** : Suivi des calories et statistiques
- **Nike Training Club** : Design moderne et motivation
- **Fitbit** : Défis et objectifs hebdomadaires
- **Apple Fitness** : Interface épurée et cartes modernes

## 🚀 Technologies utilisées
- **Flutter** 3.8.1+
- **Material Design 3**
- **Dart** avec widgets personnalisés
- **Responsive Design** pour tous les écrans

## 🎯 Navigation
- **Accueil** : Dashboard principal avec toutes les informations
- **Workout** : Section entraînements (à développer)
- **Stats** : Analyses et progrès détaillés (à développer)
- **Profil** : Paramètres utilisateur (à développer)

## 🚀 Lancement de l'application

```bash
flutter run -d chrome
```

L'application se lance automatiquement dans Chrome avec hot reload activé.
