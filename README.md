# FitTracker Pro 🏋️‍♂️

Une application de fitness moderne et intuitive développée avec Flutter.

## ✨ Fonctionnalités

### 🏠 Page d'accueil moderne
- **Salutation personnalisée** avec emoji selon l'heure
- **Notifications** avec indicateur visuel
- **Design moderne** avec couleurs et ombres élégantes

### 📅 Calendrier horizontal interactif
- Navigation fluide entre les jours
- Sélection de date intuitive
- Indicateur visuel pour aujourd'hui
- Design responsive avec animations

### 🔥 Section Streak & Motivation
- Suivi des jours consécutifs d'entraînement
- Messages motivationnels
- Design avec gradient et ombres

### 📊 Statistiques du jour
- **Calories brûlées** avec progression
- **Nombre de pas** avec objectif
- **Temps actif** quotidien
- **Consommation d'eau** avec suivi

### 💪 Entraînement du jour
- Suggestion d'entraînement personnalisée
- Informations détaillées (durée, niveau)
- Bouton de démarrage rapide

### 🎯 Défis de la semaine
- Objectifs hebdomadaires
- Barres de progression visuelles
- Suivi des calories et séances

### 📈 Activités récentes
- Historique des derniers entraînements
- Détails de chaque activité
- Horodatage et métriques

## 🎨 Design inspiré des meilleures apps
- **Strava** : Calendrier horizontal et suivi d'activités
- **MyFitnessPal** : Suivi des calories et statistiques
- **Nike Training Club** : Design moderne et motivation
- **Fitbit** : Défis et objectifs hebdomadaires
- **Apple Fitness** : Interface épurée et cartes modernes

## 🚀 Technologies utilisées
- **Flutter** 3.8.1+
- **Material Design 3**
- **Dart** avec widgets personnalisés
- **Responsive Design** pour tous les écrans

## 🎯 Navigation
- **Accueil** : Dashboard principal avec toutes les informations
- **Workout** : Section entraînements (à développer)
- **Stats** : Analyses et progrès détaillés (à développer)
- **Profil** : Paramètres utilisateur (à développer)

## 🎨 Design Énergique et Motivant

### 🔥 Couleurs Ultra-Dynamiques
- **Rouge Feu** (#FF3838) : Calories et défis brûlants
- **Violet Intense** (#5F27CD) : Entraînements et musculation
- **Bleu Électrique** (#00D2FF) : Pas et activités cardio
- **Vert Énergie** (#2ED573) : Hydratation et yoga
- **Orange Explosion** (#FF6B35) : Actions et boutons

### 💪 Messages Ultra-Motivants
- **"CHAMPION !"** au lieu de simple prénom
- **"ÉCRASEZ VOS OBJECTIFS !"** pour motiver
- **"BRÛLEZ TOUT !"** pour les entraînements
- **Emojis énergiques** : 🔥💪🚀⚡💥

### 🎯 Navigation Ultra-Moderne
- **Barre flottante** agrandie et élégante (300x65px)
- **Mode adaptatif** : Noir (#1A1A1A) ou Blanc selon le thème
- **Sélection intelligente** : Blanc en mode sombre, Violet en mode clair
- **Animations fluides** : 200ms avec AnimatedContainer
- **Positionnement parfait** : centrée à 25px du bas
- **4 pages complètes** : Accueil, Entraînements, Progrès, Profil

### 🌓 Mode Sombre/Clair
- **Bouton de basculement** dans l'en-tête de l'accueil
- **Navigation adaptative** : couleurs qui s'ajustent automatiquement
- **Icônes dynamiques** : soleil/lune selon le mode actuel
- **Paramètres sauvegardés** dans la page Profil

## 📱 Pages Complètes

### 🏠 **Page Accueil**
- **En-tête motivant** avec salutation personnalisée
- **Bouton mode sombre/clair** intégré
- **Calendrier horizontal** avec jours de la semaine
- **Streak de motivation** avec gradient orange-rouge
- **Statistiques énergiques** : Calories, Pas, Entraînements, Hydratation
- **Section entraînements** avec boutons d'action
- **Défis et activités** avec emojis motivants

### 💪 **Page Entraînements** (Style Popular Exercise)
- **Exercices populaires** : Dumbbell, Free hand, Yoga
- **Cards colorées** avec icônes et durées
- **Catégories** : Cardio, Force, Flexibilité, Endurance
- **Entraînements récents** avec historique
- **Design inspiré** de votre image de référence

### 📊 **Page Progrès/Stats**
- **Progrès hebdomadaire** avec gradient violet
- **Statistiques mensuelles** en grille
- **Réalisations récentes** avec trophées
- **Suivi détaillé** des performances

### 👤 **Page Profil**
- **Profil utilisateur** avec avatar gradient
- **Statistiques personnelles** : Entraînements, Streak, Objectifs
- **Paramètres** : Mode sombre, Notifications
- **Menu complet** : Objectifs, Historique, Partage, Aide

## 🚀 Lancement de l'application

```bash
flutter run -d chrome
```

L'application se lance automatiquement dans Chrome avec hot reload activé.

## 🎯 Prochaines fonctionnalités
- **Animations** de transition entre les pages
- **Sons motivants** lors des interactions
- **Vibrations** pour les notifications
- **Mode sombre** énergique
- **Personnalisation** des couleurs par utilisateur
