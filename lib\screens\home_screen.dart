import 'package:flutter/material.dart';
import 'home_page.dart';
import 'workout_page.dart';
import 'progress_page.dart';
import 'profile_page.dart';

class HomeScreen extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback onThemeToggle;

  const HomeScreen({
    super.key,
    required this.isDarkMode,
    required this.onThemeToggle,
  });

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _selectedIndex = 0;
  late List<Widget> _pages;

  @override
  void initState() {
    super.initState();
    _pages = [
      HomePage(isDarkMode: widget.isDarkMode, onThemeToggle: widget.onThemeToggle),
      const WorkoutPage(),
      const ProgressPage(),
      ProfilePage(isDarkMode: widget.isDarkMode, onThemeToggle: widget.onThemeToggle),
    ];
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    return Scaffold(
      backgroundColor: isDark ? const Color(0xFF1A1A1A) : Colors.white,
      body: _pages[_selectedIndex],
      bottomNavigationBar: Container(
        height: 100,
        color: Colors.transparent,
        child: Stack(
          children: [
            Positioned(
              bottom: 20,
              left: 40,
              right: 40,
              child: Container(
                height: 65,
                decoration: BoxDecoration(
                  color: isDark ? const Color(0xFF1A1A1A) : Colors.white,
                  borderRadius: BorderRadius.circular(32.5),
                  boxShadow: [
                    BoxShadow(
                      color: isDark
                          ? Colors.black.withValues(alpha: 0.4)
                          : Colors.black.withValues(alpha: 0.15),
                      blurRadius: 25,
                      offset: const Offset(0, 8),
                    ),
                  ],
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildNavItem(0, Icons.home_rounded),
                    _buildNavItem(1, Icons.bar_chart_rounded),
                    _buildNavItem(2, Icons.fitness_center_rounded),
                    _buildNavItem(3, Icons.person_rounded),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNavItem(int index, IconData icon) {
    final isSelected = _selectedIndex == index;
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedIndex = index;
        });
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        width: 45,
        height: 45,
        decoration: BoxDecoration(
          color: isSelected
            ? (isDark ? Colors.white : Colors.black)
            : Colors.transparent,
          shape: BoxShape.circle,
          boxShadow: isSelected ? [
            BoxShadow(
              color: (isDark ? Colors.white : Colors.black).withValues(alpha: 0.2),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ] : null,
        ),
        child: Icon(
          icon,
          size: 22,
          color: isSelected
            ? (isDark ? Colors.black : Colors.white)
            : (isDark ? Colors.grey[400] : Colors.grey[600]),
        ),
      ),
    );
  }
}
